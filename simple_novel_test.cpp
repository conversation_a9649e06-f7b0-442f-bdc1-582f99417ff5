#include <iostream>
#include <fstream>
#include <string>
#include <sstream>
#include <chrono>
#include <filesystem>

// Simple test to verify novel file format
int main() {
    std::cout << "Testing Novel File Format\n";
    std::cout << "=========================\n";
    
    // Create test directory
    std::filesystem::create_directories("test_output/123456_test_author/Novel/test_series");
    
    // Generate novel content according to PRD format
    std::ostringstream oss;
    oss << "Title: 测试小说\n";
    oss << "Author_UID: 123456\n";
    oss << "Author_Username: 测试作者\n";
    oss << "Novel_PID: 12345678\n";
    oss << "Novel_Type: Novel\n";
    oss << "Upload_Date: 2025-06-02T02:30:00Z\n";
    oss << "Tags: [小说, 测试, 原创]\n";
    oss << "Series_Title: 测试系列\n";
    oss << "Series_ID: series123\n";
    oss << "R18: False\n";
    oss << "Like_Count: 100\n";
    oss << "Bookmark_Count: 50\n";
    oss << "Word_Count: 1500\n";
    oss << "Description:\n这是一个测试小说的描述\n包含多行内容\n";
    oss << "Download_Time: 2025-06-02T02:30:00Z\n";
    oss << "*** Content ***\n";
    oss << "这是小说的正文内容。\n\n";
    oss << "第一章：开始\n\n";
    oss << "在一个阳光明媚的早晨，主人公开始了他的冒险之旅。\n\n";
    oss << "第二章：冒险\n\n";
    oss << "经过重重困难，主人公终于找到了宝藏。\n\n";
    oss << "完。";
    
    std::string file_path = "test_output/123456_test_author/Novel/test_series/20250602_12345678_test_novel.txt";
    
    // Write to file
    std::ofstream file(file_path);
    if (!file.is_open()) {
        std::cout << "✗ Failed to create file: " << file_path << "\n";
        return 1;
    }
    
    file << oss.str();
    file.close();
    
    std::cout << "✓ Novel file created successfully!\n";
    std::cout << "  File path: " << file_path << "\n";
    std::cout << "  Content length: " << oss.str().length() << " bytes\n";
    
    // Verify file exists and read content
    std::ifstream read_file(file_path);
    if (read_file.is_open()) {
        std::string content((std::istreambuf_iterator<char>(read_file)),
                           std::istreambuf_iterator<char>());
        read_file.close();
        
        std::cout << "\nFile content preview (first 300 chars):\n";
        std::cout << "----------------------------------------\n";
        std::cout << content.substr(0, 300) << "...\n";
        std::cout << "----------------------------------------\n";
        
        // Check if content contains expected elements
        bool has_title = content.find("Title: 测试小说") != std::string::npos;
        bool has_novel_type = content.find("Novel_Type: Novel") != std::string::npos;
        bool has_content_separator = content.find("*** Content ***") != std::string::npos;
        bool has_novel_text = content.find("这是小说的正文内容") != std::string::npos;
        bool has_series = content.find("Series_Title: 测试系列") != std::string::npos;
        
        std::cout << "\nContent validation:\n";
        std::cout << "  Has title: " << (has_title ? "✓" : "✗") << "\n";
        std::cout << "  Has novel type: " << (has_novel_type ? "✓" : "✗") << "\n";
        std::cout << "  Has series info: " << (has_series ? "✓" : "✗") << "\n";
        std::cout << "  Has content separator: " << (has_content_separator ? "✓" : "✗") << "\n";
        std::cout << "  Has novel text: " << (has_novel_text ? "✓" : "✗") << "\n";
        
        if (has_title && has_novel_type && has_content_separator && has_novel_text && has_series) {
            std::cout << "\n✓ All validation checks passed!\n";
            std::cout << "✓ Novel file format is correct according to PRD specification!\n";
            return 0;
        } else {
            std::cout << "\n✗ Some validation checks failed!\n";
            return 1;
        }
    } else {
        std::cout << "✗ Could not read the saved file!\n";
        return 1;
    }
}
