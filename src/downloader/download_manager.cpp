#include "downloader.h"
#include "utils.h"
#include "storage.h"
#include <spdlog/spdlog.h>
#include <thread>
#include <chrono>
#include <fstream>
#include <sstream>

namespace pixiv {

// DirectDownloader implementation
DirectDownloader::DirectDownloader(std::shared_ptr<HttpClient> http_client)
    : http_client_(http_client), cancelled_(false), busy_(false) {
}

DirectDownloader::~DirectDownloader() = default;

DownloadResult DirectDownloader::download_file(const DownloadTask& task,
                                             std::function<void(size_t, size_t)> progress_callback) {
    DownloadResult result;
    result.success = false;
    result.local_path = task.local_path;
    result.bytes_downloaded = 0;

    auto start_time = std::chrono::steady_clock::now();

    try {
        busy_ = true;

        if (cancelled_) {
            result.error_message = "Download cancelled";
            busy_ = false;
            return result;
        }

        // Handle different file types
        if (task.is_metadata_file) {
            result.success = save_metadata_file(task);
            if (result.success) {
                result.bytes_downloaded = 1024; // Approximate metadata size
            }
        } else if (task.is_novel_file) {
            result.success = save_novel_file(task);
            if (result.success) {
                result.bytes_downloaded = task.artwork.novel_content.length();
            }
        } else {
            // Download actual image file
            result.success = http_client_->download_file(task.url, task.local_path, progress_callback);
            if (result.success) {
                // Get actual file size
                std::ifstream file(task.local_path, std::ios::binary | std::ios::ate);
                if (file.is_open()) {
                    result.bytes_downloaded = file.tellg();
                    file.close();
                }
            }
        }

        if (!result.success) {
            result.error_message = "Download failed for: " + task.url;
        }

    } catch (const std::exception& e) {
        result.error_message = "Download failed: " + std::string(e.what());
    }

    auto end_time = std::chrono::steady_clock::now();
    result.duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);

    busy_ = false;
    return result;
}

std::vector<DownloadResult> DirectDownloader::download_files(const std::vector<DownloadTask>& tasks,
                                                           std::function<void(const DownloadProgress&)> progress_callback) {
    std::vector<DownloadResult> results;
    
    DownloadProgress progress;
    progress.total_artworks = 0;
    progress.total_files = tasks.size();
    progress.completed_artworks = 0;
    progress.completed_files = 0;
    progress.total_bytes = 0;
    progress.downloaded_bytes = 0;
    
    for (const auto& task : tasks) {
        if (cancelled_) {
            break;
        }
        
        progress.current_file = task.filename;
        if (progress_callback) {
            progress_callback(progress);
        }
        
        auto result = download_file(task);
        results.push_back(result);
        
        progress.completed_files++;
        progress.downloaded_bytes += result.bytes_downloaded;
        
        if (progress_callback) {
            progress_callback(progress);
        }
    }
    
    return results;
}

void DirectDownloader::cancel() {
    cancelled_ = true;
}

bool DirectDownloader::is_busy() const {
    return busy_;
}

bool DirectDownloader::save_metadata_file(const DownloadTask& task) {
    try {
        // Create directory if it doesn't exist
        std::string dir_path = utils::fs::get_parent_directory(task.local_path);
        if (!utils::fs::directory_exists(dir_path)) {
            if (!utils::fs::create_directories(dir_path)) {
                return false;
            }
        }

        // Generate metadata content
        std::ostringstream oss;
        oss << "Title: " << task.artwork.title << "\n";
        oss << "Author_UID: " << task.artwork.author.uid << "\n";
        oss << "Author_Username: " << task.artwork.author.username << "\n";
        oss << "Artwork_PID: " << task.artwork.pid << "\n";
        oss << "Artwork_Type: " << to_string(task.artwork.type) << "\n";

        oss << "Tags: [";
        for (size_t i = 0; i < task.artwork.tags.size(); ++i) {
            if (i > 0) oss << ", ";
            oss << task.artwork.tags[i];
        }
        oss << "]\n";

        oss << "Description:\n" << task.artwork.description << "\n";
        oss << "Upload_Date: " << utils::time::format_time(task.artwork.upload_date, "%Y-%m-%dT%H:%M:%SZ") << "\n";
        oss << "Page_Count: " << task.artwork.page_count << "\n";
        oss << "R18: " << (task.artwork.is_r18 ? "True" : "False") << "\n";
        oss << "Like_Count: " << task.artwork.like_count << "\n";
        oss << "Bookmark_Count: " << task.artwork.bookmark_count << "\n";

        if (task.artwork.type == ArtworkType::Novel) {
            oss << "Word_Count: " << task.artwork.word_count << "\n";
        }

        if (task.artwork.series) {
            oss << "Series_ID: " << task.artwork.series->id << "\n";
            oss << "Series_Title: " << task.artwork.series->title << "\n";
        }

        oss << "Original_URLs:\n";
        for (const auto& url : task.artwork.image_urls) {
            oss << url << "\n";
        }

        oss << "Download_Time: " << utils::time::current_iso8601() << "\n";

        // Write to file
        std::ofstream file(task.local_path);
        if (!file.is_open()) {
            return false;
        }

        file << oss.str();
        file.close();

        return true;

    } catch (const std::exception&) {
        return false;
    }
}

bool DirectDownloader::save_novel_file(const DownloadTask& task) {
    try {
        spdlog::debug("Saving novel file: {}", task.local_path);
        spdlog::debug("Novel content length: {}", task.artwork.novel_content.length());

        // Create directory if it doesn't exist
        std::string dir_path = utils::fs::get_parent_directory(task.local_path);
        if (!utils::fs::directory_exists(dir_path)) {
            if (!utils::fs::create_directories(dir_path)) {
                spdlog::error("Failed to create directory: {}", dir_path);
                return false;
            }
        }

        // Generate novel content according to PRD format
        std::ostringstream oss;
        oss << "Title: " << task.artwork.title << "\n";
        oss << "Author_UID: " << task.artwork.author.uid << "\n";
        oss << "Author_Username: " << task.artwork.author.username << "\n";
        oss << "Novel_PID: " << task.artwork.pid << "\n";
        oss << "Novel_Type: Novel\n";
        oss << "Upload_Date: " << utils::time::format_time(task.artwork.upload_date, "%Y-%m-%dT%H:%M:%SZ") << "\n";

        oss << "Tags: [";
        for (size_t i = 0; i < task.artwork.tags.size(); ++i) {
            if (i > 0) oss << ", ";
            oss << task.artwork.tags[i];
        }
        oss << "]\n";

        if (task.artwork.series) {
            oss << "Series_Title: " << task.artwork.series->title << "\n";
            oss << "Series_ID: " << task.artwork.series->id << "\n";
        }

        oss << "R18: " << (task.artwork.is_r18 ? "True" : "False") << "\n";
        oss << "Like_Count: " << task.artwork.like_count << "\n";
        oss << "Bookmark_Count: " << task.artwork.bookmark_count << "\n";
        oss << "Word_Count: " << task.artwork.word_count << "\n";
        oss << "Description:\n" << task.artwork.description << "\n";
        oss << "Download_Time: " << utils::time::current_iso8601() << "\n";
        oss << "*** Content ***\n";
        oss << task.artwork.novel_content;

        // Write to file
        std::ofstream file(task.local_path);
        if (!file.is_open()) {
            return false;
        }

        file << oss.str();
        file.close();

        return true;

    } catch (const std::exception&) {
        return false;
    }
}

// DownloadManager implementation
DownloadManager::DownloadManager(std::unique_ptr<Downloader> downloader,
                               int concurrency,
                               const std::string& output_dir,
                               const PathTemplate& path_template,
                               ConflictStrategy conflict_strategy)
    : downloader_(std::move(downloader))
    , concurrency_(concurrency)
    , output_dir_(output_dir)
    , path_template_(path_template)
    , conflict_strategy_(conflict_strategy)
    , cancelled_(false)
    , busy_(false)
    , producer_finished_(false) {
}

DownloadManager::~DownloadManager() {
    cancel();
    
    // Wait for worker threads to finish
    for (auto& thread : worker_threads_) {
        if (thread.joinable()) {
            thread.join();
        }
    }
}

bool DownloadManager::start_download(const std::vector<ArtworkInfo>& artworks,
                                    std::function<void(const DownloadProgress&)> progress_callback) {
    if (busy_) {
        return false;
    }
    
    busy_ = true;
    cancelled_ = false;
    producer_finished_ = false;
    progress_callback_ = progress_callback;
    
    // Initialize progress
    {
        std::lock_guard<std::mutex> lock(progress_mutex_);
        progress_.total_artworks = artworks.size();
        progress_.completed_artworks = 0;
        progress_.total_files = 0;
        progress_.completed_files = 0;
        progress_.failed_files = 0;
        progress_.total_bytes = 0;
        progress_.downloaded_bytes = 0;
        progress_.current_file_bytes = 0;
        progress_.current_file_total_bytes = 0;
        progress_.current_file = "";
        progress_.current_file_progress = 0.0;

        // Calculate total files
        for (const auto& artwork : artworks) {
            if (artwork.type == ArtworkType::Novel) {
                progress_.total_files += 2; // Novel file + metadata
            } else {
                progress_.total_files += artwork.page_count + 1; // Image files + metadata
            }
        }
    }
    
    try {
        // Start producer thread
        std::thread producer(&DownloadManager::producer_thread, this, std::cref(artworks));
        
        // Start consumer threads
        for (int i = 0; i < concurrency_; ++i) {
            worker_threads_.emplace_back(&DownloadManager::consumer_thread, this);
        }
        
        // Wait for producer to finish
        producer.join();

        // Wait for workers to finish (they will exit when producer_finished_ is true and queue is empty)
        for (auto& thread : worker_threads_) {
            if (thread.joinable()) {
                thread.join();
            }
        }
        worker_threads_.clear();

        // Final progress update to ensure UI is properly updated
        if (progress_callback_) {
            progress_callback_(get_progress());
        }
        
    } catch (const std::exception& e) {
        cancel();
        busy_ = false;
        return false;
    }
    
    busy_ = false;
    return !cancelled_;
}

void DownloadManager::cancel() {
    cancelled_ = true;
    queue_cv_.notify_all();
    
    if (downloader_) {
        downloader_->cancel();
    }
}

bool DownloadManager::is_busy() const {
    return busy_;
}

DownloadProgress DownloadManager::get_progress() const {
    std::lock_guard<std::mutex> lock(const_cast<std::mutex&>(progress_mutex_));
    return progress_;
}

void DownloadManager::producer_thread(const std::vector<ArtworkInfo>& artworks) {
    try {
        for (const auto& artwork : artworks) {
            if (cancelled_) {
                break;
            }
            
            auto tasks = generate_download_tasks(artwork);
            
            {
                std::lock_guard<std::mutex> lock(queue_mutex_);
                for (const auto& task : tasks) {
                    task_queue_.push(task);
                }
            }
            queue_cv_.notify_all();
            
            // Update progress
            {
                std::lock_guard<std::mutex> lock(progress_mutex_);
                progress_.completed_artworks++;
            }
            
            if (progress_callback_) {
                progress_callback_(get_progress());
            }
        }
        
    } catch (const std::exception&) {
        // Error in producer
    }
    
    producer_finished_ = true;
    queue_cv_.notify_all();
}

void DownloadManager::consumer_thread() {
    while (!cancelled_) {
        DownloadTask task;
        bool has_task = false;

        {
            std::unique_lock<std::mutex> lock(queue_mutex_);
            queue_cv_.wait(lock, [this] { return !task_queue_.empty() || cancelled_ || producer_finished_; });

            if (!task_queue_.empty()) {
                task = task_queue_.front();
                task_queue_.pop();
                has_task = true;
            } else if (producer_finished_) {
                // No more tasks and producer is finished, exit
                break;
            }
        }

        if (has_task && !cancelled_) {
            // Process real download task
            auto result = downloader_->download_file(task, [this](size_t downloaded, size_t total) {
                std::lock_guard<std::mutex> lock(progress_mutex_);
                progress_.current_file_bytes = downloaded;
                progress_.current_file_total_bytes = total;
            });

            {
                std::lock_guard<std::mutex> lock(progress_mutex_);
                progress_.completed_files++;
                progress_.downloaded_bytes += result.bytes_downloaded;
                progress_.current_file = task.filename;

                if (!result.success) {
                    progress_.failed_files++;
                    // Log error but continue with other downloads
                    spdlog::error("Failed to download {}: {}", task.filename, result.error_message);
                }
            }

            if (progress_callback_) {
                progress_callback_(get_progress());
            }
        }
    }
}

std::vector<DownloadTask> DownloadManager::generate_download_tasks(const ArtworkInfo& artwork) {
    std::vector<DownloadTask> tasks;

    // Use the configured storage manager to generate proper paths
    StorageManager storage_manager(output_dir_, path_template_, conflict_strategy_);

    // For novels, only generate a single novel file task (no image downloads)
    if (artwork.type == ArtworkType::Novel) {
        DownloadTask novel_task;
        novel_task.artwork = artwork;
        novel_task.page_index = 0;
        novel_task.is_metadata_file = false;
        novel_task.is_novel_file = true;

        // Use StorageManager to generate proper file path for novel
        novel_task.local_path = storage_manager.generate_file_path(artwork, 0, false);
        novel_task.filename = utils::fs::get_filename(novel_task.local_path);
        novel_task.url = ""; // No URL needed for novels

        tasks.push_back(novel_task);
    } else {
        // Generate tasks for each page (for Illust/Manga)
        for (int i = 0; i < artwork.page_count; ++i) {
            DownloadTask task;
            task.artwork = artwork;
            task.page_index = i;
            task.is_metadata_file = false;
            task.is_novel_file = false;

            // Use StorageManager to generate proper file path (includes subdirectory for multi-page works)
            task.local_path = storage_manager.generate_file_path(artwork, i, false);
            task.filename = utils::fs::get_filename(task.local_path);

            // Set URL
            if (i < static_cast<int>(artwork.image_urls.size())) {
                task.url = artwork.image_urls[i];
            } else {
                task.url = ""; // No URL for missing images
            }

            tasks.push_back(task);
        }
    }

    // Generate metadata task
    DownloadTask metadata_task;
    metadata_task.artwork = artwork;
    metadata_task.page_index = -1;
    metadata_task.is_metadata_file = true;
    metadata_task.is_novel_file = false;

    // Use StorageManager to generate proper metadata file path
    metadata_task.local_path = storage_manager.generate_file_path(artwork, -1, true);
    metadata_task.filename = utils::fs::get_filename(metadata_task.local_path);
    metadata_task.url = "";
    tasks.push_back(metadata_task);

    return tasks;
}

// Factory functions
std::unique_ptr<Downloader> create_downloader(DownloadMethod method,
                                             std::shared_ptr<HttpClient> http_client,
                                             const Aria2Config& aria2_config) {
    (void)aria2_config; // Suppress unused parameter warning for now

    switch (method) {
        case DownloadMethod::Direct:
            if (http_client) {
                return std::make_unique<DirectDownloader>(http_client);
            }
            break;
        case DownloadMethod::Aria2c:
            spdlog::warn("Aria2c downloader not yet implemented, falling back to direct download");
            if (http_client) {
                return std::make_unique<DirectDownloader>(http_client);
            }
            break;
        case DownloadMethod::Aria2RPC:
            spdlog::warn("Aria2 RPC downloader not yet implemented, falling back to direct download");
            if (http_client) {
                return std::make_unique<DirectDownloader>(http_client);
            }
            break;
    }

    return nullptr;
}

} // namespace pixiv
