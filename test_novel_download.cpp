#include "downloader.h"
#include "types.h"
#include "utils.h"
#include <iostream>
#include <memory>
#include <fstream>
#include <spdlog/spdlog.h>

using namespace pixiv;

int main() {
    // Set up logging
    spdlog::set_level(spdlog::level::debug);
    
    std::cout << "Testing Novel Download Functionality\n";
    std::cout << "====================================\n";
    
    // Create a mock novel artwork
    ArtworkInfo novel;
    novel.pid = "12345678";
    novel.type = ArtworkType::Novel;
    novel.title = "测试小说";
    novel.safe_title = "test_novel";
    novel.description = "这是一个测试小说的描述\n包含多行内容";
    novel.upload_date = std::chrono::system_clock::now();
    novel.tags = {"小说", "测试", "原创"};
    novel.is_r18 = false;
    novel.like_count = 100;
    novel.bookmark_count = 50;
    novel.word_count = 1500;
    novel.novel_content = "这是小说的正文内容。\n\n第一章：开始\n\n在一个阳光明媚的早晨，主人公开始了他的冒险之旅。\n\n第二章：冒险\n\n经过重重困难，主人公终于找到了宝藏。\n\n完。";
    
    // Set up author info
    novel.author.uid = "123456";
    novel.author.username = "测试作者";
    novel.author.safe_username = "test_author";
    
    // Set up series info
    novel.series = std::make_shared<SeriesInfo>();
    novel.series->id = "series123";
    novel.series->title = "测试系列";
    novel.series->safe_title = "test_series";
    
    // Create download task
    DownloadTask task;
    task.artwork = novel;
    task.local_path = "test_output/123456_test_author/Novel/test_series/20250602_12345678_test_novel.txt";
    task.filename = "20250602_12345678_test_novel.txt";
    task.is_novel_file = true;
    task.is_metadata_file = false;
    task.page_index = 0;
    task.url = "";
    
    // Create a mock HTTP client (not needed for novel files)
    std::shared_ptr<HttpClient> http_client = nullptr;
    
    // Create DirectDownloader
    auto downloader = std::make_unique<DirectDownloader>(http_client);
    
    std::cout << "Testing novel file download...\n";
    
    // Test the download
    auto result = downloader->download_file(task);
    
    if (result.success) {
        std::cout << "✓ Novel download successful!\n";
        std::cout << "  File saved to: " << result.local_path << "\n";
        std::cout << "  Bytes written: " << result.bytes_downloaded << "\n";
        std::cout << "  Duration: " << result.duration.count() << "ms\n";
        
        // Verify file exists and has content
        std::ifstream file(result.local_path);
        if (file.is_open()) {
            std::string content((std::istreambuf_iterator<char>(file)),
                               std::istreambuf_iterator<char>());
            file.close();
            
            std::cout << "\nFile content preview (first 200 chars):\n";
            std::cout << "----------------------------------------\n";
            std::cout << content.substr(0, 200) << "...\n";
            std::cout << "----------------------------------------\n";
            
            // Check if content contains expected elements
            bool has_title = content.find("Title: 测试小说") != std::string::npos;
            bool has_content_separator = content.find("*** Content ***") != std::string::npos;
            bool has_novel_text = content.find("这是小说的正文内容") != std::string::npos;
            
            std::cout << "\nContent validation:\n";
            std::cout << "  Has title: " << (has_title ? "✓" : "✗") << "\n";
            std::cout << "  Has content separator: " << (has_content_separator ? "✓" : "✗") << "\n";
            std::cout << "  Has novel text: " << (has_novel_text ? "✓" : "✗") << "\n";
            
            if (has_title && has_content_separator && has_novel_text) {
                std::cout << "\n✓ All validation checks passed!\n";
                return 0;
            } else {
                std::cout << "\n✗ Some validation checks failed!\n";
                return 1;
            }
        } else {
            std::cout << "✗ Could not read the saved file!\n";
            return 1;
        }
    } else {
        std::cout << "✗ Novel download failed!\n";
        std::cout << "  Error: " << result.error_message << "\n";
        return 1;
    }
}
